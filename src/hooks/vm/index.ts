/**
 * VM Hooks Index
 * Exports all VM-related custom React hooks
 */

// Export VM Connection hooks
export { 
  useVMConnection, 
  useVMSingleConnection 
} from './useVMConnection';

// Export VM Docker hooks
export { 
  useVMDocker, 
  useVMDockerForVM 
} from './useVMDocker';

// Export VM Monitoring hooks
export { 
  useVMMonitoring, 
  useVMMonitoringForVM 
} from './useVMMonitoring';

// Export VM Security hooks
export { 
  useVMSecurity, 
  useVMSecurityForVM 
} from './useVMSecurity';

// Export VM Health hooks
export { 
  useVMHealth, 
  useVMHealthForVM, 
  useVMHealthMonitor 
} from './useVMHealth';

// Re-export types for convenience
export type {
  UseVMConnectionReturn,
  UseVMDockerReturn,
  UseVMMonitoringReturn,
  UseVMSecurityReturn,
  UseVMHealthReturn,
  VMConnectionEventHandler,
  VMDockerEventHandler,
  VMMonitoringEventHandler,
  VMSecurityEventHandler,
  VMHealthEventHandler
} from '@/types/vm-frontend';
