/**
 * VM Status Indicator Component
 * Displays VM connection and health status with visual indicators
 */

'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { 
  CheckCircle, 
  XCircle, 
  AlertCircle, 
  Clock, 
  Loader2,
  Wifi,
  WifiOff,
  Activity,
  AlertTriangle
} from 'lucide-react';
import { BaseVMComponentProps, VMStatusType, VMAlertSeverity } from '@/types/vm-frontend';

interface VMStatusIndicatorProps extends BaseVMComponentProps {
  status: VMStatusType;
  label?: string;
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
  showIcon?: boolean;
  pulse?: boolean;
  severity?: VMAlertSeverity;
  lastUpdated?: Date;
  tooltip?: string;
}

const statusConfig = {
  connected: {
    icon: CheckCircle,
    color: 'text-green-500',
    bgColor: 'bg-green-500/10',
    borderColor: 'border-green-500/20',
    label: 'Connected',
    pulseColor: 'bg-green-500'
  },
  disconnected: {
    icon: XCircle,
    color: 'text-red-500',
    bgColor: 'bg-red-500/10',
    borderColor: 'border-red-500/20',
    label: 'Disconnected',
    pulseColor: 'bg-red-500'
  },
  connecting: {
    icon: Loader2,
    color: 'text-blue-500',
    bgColor: 'bg-blue-500/10',
    borderColor: 'border-blue-500/20',
    label: 'Connecting',
    pulseColor: 'bg-blue-500'
  },
  error: {
    icon: AlertCircle,
    color: 'text-red-500',
    bgColor: 'bg-red-500/10',
    borderColor: 'border-red-500/20',
    label: 'Error',
    pulseColor: 'bg-red-500'
  },
  unknown: {
    icon: AlertTriangle,
    color: 'text-gray-500',
    bgColor: 'bg-gray-500/10',
    borderColor: 'border-gray-500/20',
    label: 'Unknown',
    pulseColor: 'bg-gray-500'
  }
};

const sizeConfig = {
  sm: {
    container: 'h-6 px-2 text-xs',
    icon: 'h-3 w-3',
    dot: 'h-2 w-2',
    text: 'text-xs'
  },
  md: {
    container: 'h-8 px-3 text-sm',
    icon: 'h-4 w-4',
    dot: 'h-3 w-3',
    text: 'text-sm'
  },
  lg: {
    container: 'h-10 px-4 text-base',
    icon: 'h-5 w-5',
    dot: 'h-4 w-4',
    text: 'text-base'
  }
};

export function VMStatusIndicator({
  status,
  label,
  size = 'md',
  showLabel = true,
  showIcon = true,
  pulse = false,
  severity,
  lastUpdated,
  tooltip,
  className,
  animate = true,
  variant = 'default',
  ...props
}: VMStatusIndicatorProps) {
  const config = statusConfig[status];
  const sizeStyles = sizeConfig[size];
  const Icon = config.icon;

  const displayLabel = label || config.label;

  const containerVariants = {
    initial: { opacity: 0, scale: 0.8 },
    animate: { 
      opacity: 1, 
      scale: 1,
      transition: {
        duration: 0.2,
        ease: "easeOut" as const
      }
    },
    exit: { 
      opacity: 0, 
      scale: 0.8,
      transition: {
        duration: 0.15,
        ease: "easeOut" as const
      }
    }
  };

  const pulseVariants = {
    pulse: {
      scale: [1, 1.2, 1],
      opacity: [0.7, 1, 0.7],
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: "easeInOut" as const
      }
    }
  };

  const getVariantStyles = () => {
    switch (variant) {
      case 'glass':
        return 'backdrop-blur-sm bg-white/10 dark:bg-black/10 border border-white/20 dark:border-white/10';
      case 'gradient':
        return 'bg-gradient-to-r from-white/5 to-white/10 dark:from-black/5 dark:to-black/10 border border-white/10';
      default:
        return cn(config.bgColor, config.borderColor, 'border');
    }
  };

  const formatLastUpdated = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  };

  const Component = animate ? motion.div : 'div';
  const componentProps = animate ? {
    variants: containerVariants,
    initial: 'initial',
    animate: 'animate',
    exit: 'exit'
  } : {};

  return (
    <Component
      className={cn(
        'inline-flex items-center gap-2 rounded-full transition-all duration-200',
        sizeStyles.container,
        getVariantStyles(),
        'hover:shadow-sm',
        className
      )}
      title={tooltip || `Status: ${displayLabel}${lastUpdated ? ` (${formatLastUpdated(lastUpdated)})` : ''}`}
      {...componentProps}
      {...props}
    >
      {showIcon && (
        <div className="relative">
          <Icon 
            className={cn(
              sizeStyles.icon,
              config.color,
              status === 'connecting' && 'animate-spin'
            )}
          />
          {pulse && (
            <motion.div
              className={cn(
                'absolute inset-0 rounded-full',
                config.pulseColor,
                'opacity-20'
              )}
              variants={pulseVariants}
              animate="pulse"
            />
          )}
        </div>
      )}

      {showLabel && (
        <span className={cn(
          'font-medium',
          config.color,
          sizeStyles.text
        )}>
          {displayLabel}
        </span>
      )}

      {lastUpdated && (
        <span className={cn(
          'text-muted-foreground',
          size === 'sm' ? 'text-xs' : 'text-xs'
        )}>
          {formatLastUpdated(lastUpdated)}
        </span>
      )}

      {severity && (
        <div className={cn(
          'rounded-full',
          sizeStyles.dot,
          severity === 'critical' && 'bg-red-500',
          severity === 'high' && 'bg-orange-500',
          severity === 'medium' && 'bg-yellow-500',
          severity === 'low' && 'bg-blue-500'
        )} />
      )}
    </Component>
  );
}

// Specialized status indicators
export function VMConnectionStatus({
  isConnected,
  isConnecting,
  error,
  ...props
}: Omit<VMStatusIndicatorProps, 'status'> & {
  isConnected: boolean;
  isConnecting: boolean;
  error?: string;
}) {
  const getStatus = (): VMStatusType => {
    if (error) return 'error';
    if (isConnecting) return 'connecting';
    if (isConnected) return 'connected';
    return 'disconnected';
  };

  return (
    <VMStatusIndicator
      status={getStatus()}
      tooltip={error || undefined}
      pulse={isConnecting}
      {...props}
    />
  );
}

export function VMHealthStatus({
  health,
  ...props
}: Omit<VMStatusIndicatorProps, 'status'> & {
  health: 'healthy' | 'degraded' | 'unhealthy' | 'unknown';
}) {
  const getStatus = (): VMStatusType => {
    switch (health) {
      case 'healthy': return 'connected';
      case 'degraded': return 'connecting';
      case 'unhealthy': return 'error';
      default: return 'unknown';
    }
  };

  const getLabel = () => {
    switch (health) {
      case 'healthy': return 'Healthy';
      case 'degraded': return 'Degraded';
      case 'unhealthy': return 'Unhealthy';
      default: return 'Unknown';
    }
  };

  return (
    <VMStatusIndicator
      status={getStatus()}
      label={getLabel()}
      {...props}
    />
  );
}

export function VMServiceStatus({
  serviceName,
  isRunning,
  isLoading,
  error,
  ...props
}: Omit<VMStatusIndicatorProps, 'status' | 'label'> & {
  serviceName: string;
  isRunning: boolean;
  isLoading: boolean;
  error?: string;
}) {
  const getStatus = (): VMStatusType => {
    if (error) return 'error';
    if (isLoading) return 'connecting';
    if (isRunning) return 'connected';
    return 'disconnected';
  };

  return (
    <VMStatusIndicator
      status={getStatus()}
      label={serviceName}
      tooltip={error || undefined}
      pulse={isLoading}
      {...props}
    />
  );
}

// Status indicator with custom colors
export function VMCustomStatusIndicator({
  color,
  bgColor,
  borderColor,
  icon: CustomIcon,
  ...props
}: VMStatusIndicatorProps & {
  color?: string;
  bgColor?: string;
  borderColor?: string;
  icon?: React.ComponentType<any>;
}) {
  const Icon = CustomIcon || statusConfig[props.status].icon;

  return (
    <div
      className={cn(
        'inline-flex items-center gap-2 rounded-full transition-all duration-200',
        sizeConfig[props.size || 'md'].container,
        bgColor || statusConfig[props.status].bgColor,
        borderColor || statusConfig[props.status].borderColor,
        'border hover:shadow-sm',
        props.className
      )}
      title={props.tooltip}
    >
      <Icon 
        className={cn(
          sizeConfig[props.size || 'md'].icon,
          color || statusConfig[props.status].color,
          props.status === 'connecting' && 'animate-spin'
        )}
      />
      {props.showLabel !== false && (
        <span className={cn(
          'font-medium',
          color || statusConfig[props.status].color,
          sizeConfig[props.size || 'md'].text
        )}>
          {props.label || statusConfig[props.status].label}
        </span>
      )}
    </div>
  );
}
